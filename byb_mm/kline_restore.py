import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def process_bybusdt_trades_to_kline():
    """
    将bybusdt交易记录整理成1分钟K线数据
    数据格式：时间戳(秒) amount交易额 vol交易量 open开盘价 close收盘价 high最高价 low最低价
    """
    print("正在读取Excel文件...")

    # 读取Excel文件
    df = pd.read_excel('用户实时交易单2025-07-03-2025-07-03.xls')
    print(f"总交易记录数: {len(df)}")

    # 筛选bybusdt交易对
    df = df[df['币对'] == 'bybusdt']
    print(f"bybusdt交易记录数: {len(df)}")

    if len(df) == 0:
        print("未找到bybusdt交易记录")
        return

    # 数据预处理
    print("正在处理数据...")

    # 转换时间格式
    df['timestamp'] = pd.to_datetime(df['成交時間'])

    # 转换数值类型 - 处理可能的字符串格式
    df['成交数量'] = pd.to_numeric(df['成交数量'].astype(str).str.replace(' BYB', ''), errors='coerce')
    df['成交价格'] = pd.to_numeric(df['成交价格'].astype(str).str.replace(' USDT', ''), errors='coerce')

    # 计算交易额 (成交数量 * 成交价格)
    df['交易额'] = df['成交数量'] * df['成交价格']

    # 删除无效数据
    df = df.dropna(subset=['成交数量', '成交价格', '交易额'])
    print(f"有效交易记录数: {len(df)}")

    # 按时间排序
    df = df.sort_values('timestamp')

    # 设置时间为索引，并重采样为1分钟
    df.set_index('timestamp', inplace=True)

    print("正在生成1分钟K线数据...")

    # 生成1分钟K线数据
    kline_data = df.resample('1T').agg({
        '成交价格': ['first', 'max', 'min', 'last'],  # 开盘价、最高价、最低价、收盘价
        '交易额': 'sum',                              # 交易额总和
        '成交数量': 'sum'                             # 交易量总和
    }).dropna()

    # 重命名列
    kline_data.columns = ['open', 'high', 'low', 'close', 'amount', 'vol']

    # 重置索引，获取时间戳
    kline_data.reset_index(inplace=True)

    # 转换时间戳为秒
    kline_data['timestamp_sec'] = kline_data['timestamp'].astype('int64') // 10**9

    # 重新排列列的顺序
    result = kline_data[['timestamp_sec', 'amount', 'vol', 'open', 'close', 'high', 'low']].copy()

    # 格式化数值显示
    result['amount'] = result['amount'].round(6)
    result['vol'] = result['vol'].round(6)
    result['open'] = result['open'].round(6)
    result['close'] = result['close'].round(6)
    result['high'] = result['high'].round(6)
    result['low'] = result['low'].round(6)

    print(f"生成K线数据条数: {len(result)}")
    print("\n前10条K线数据:")
    print(result.head(10).to_string(index=False))

    # 保存到CSV文件
    output_file = 'bybusdt_1min_kline_2025-07-03.csv'
    result.to_csv(output_file, index=False)
    print(f"\nK线数据已保存到: {output_file}")

    # 显示统计信息
    print(f"\n统计信息:")
    print(f"时间范围: {kline_data['timestamp'].min()} 到 {kline_data['timestamp'].max()}")
    print(f"总交易额: {result['amount'].sum():.2f} USDT")
    print(f"总交易量: {result['vol'].sum():.2f} BYB")
    print(f"价格范围: {result['low'].min():.6f} - {result['high'].max():.6f} USDT")

    return result

if __name__ == "__main__":
    # 执行K线数据生成
    kline_data = process_bybusdt_trades_to_kline()